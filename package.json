{"name": "@leeroy/mcp-superassistant-proxy", "version": "0.0.12", "description": "A proxy server for MCP (Model Context Protocol) that aggregates multiple MCP servers with native messaging support for Chrome extensions", "private": false, "keywords": ["mcp", "stdio", "sse", "gateway", "proxy", "bridge", "chrome-extension", "native-messaging"], "type": "module", "bin": {"mcp-superassistant-proxy": "dist/index.js"}, "scripts": {"build": "tsc", "start": "node dist/index.js", "postinstall": "node scripts/setup-native-messaging.js"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.12.1", "body-parser": "^1.20.3", "cors": "^2.8.5", "express": "^4.21.2", "ws": "^8.18.0", "yargs": "^17.7.2", "zod": "^3.23.8"}, "devDependencies": {"@types/body-parser": "^1.19.5", "@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/node": "^22.15.19", "@types/ws": "^8.5.13", "@types/yargs": "^17.0.33", "ts-node": "^10.9.2", "tsx": "^4.19.2", "typescript": "^5.7.3"}}