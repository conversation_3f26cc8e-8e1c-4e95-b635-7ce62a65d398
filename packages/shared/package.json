{"name": "@extension/shared", "version": "0.4.2", "description": "chrome extension - shared code", "type": "module", "private": true, "sideEffects": false, "files": ["dist/**"], "types": "index.mts", "main": "dist/index.mjs", "scripts": {"clean:bundle": "<PERSON><PERSON><PERSON> dist", "clean:node_modules": "pnpm dlx rimraf node_modules", "clean:turbo": "rimraf .turbo", "clean": "pnpm clean:bundle && pnpm clean:node_modules && pnpm clean:turbo", "ready": "tsc -b", "lint": "eslint .", "lint:fix": "pnpm lint --fix", "prettier": "prettier . --write --ignore-path ../../.prettierignore", "type-check": "tsc --noEmit"}, "devDependencies": {"@extension/storage": "workspace:*", "@extension/tsconfig": "workspace:*"}}