{"name": "@extension/vite-config", "version": "0.4.2", "description": "chrome extension - vite base configuration", "type": "module", "private": true, "sideEffects": true, "types": "index.mts", "main": "dist/index.mjs", "module": "dist/index.mjs", "scripts": {"clean:node_modules": "pnpm dlx rimraf node_modules", "clean:bundle": "pnpm dlx rimraf dist", "clean": "pnpm clean:bundle && pnpm clean:node_modules", "ready": "tsc -b"}, "dependencies": {"@extension/env": "workspace:*"}, "devDependencies": {"@extension/hmr": "workspace:*", "@extension/tsconfig": "workspace:*", "@vitejs/plugin-react-swc": "^3.7.2"}}