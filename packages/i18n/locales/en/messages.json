{"extensionDescription": {"description": "Extension description", "message": "Chrome extension boilerplate developed with Vite, React and Typescript"}, "extensionName": {"description": "Extension name", "message": "Chrome extension boilerplate"}, "toggleTheme": {"message": "Toggle theme"}, "loading": {"message": "Loading..."}, "greeting": {"description": "Greeting message", "message": "Hello, My name is $NAME$", "placeholders": {"name": {"content": "$1", "example": "<PERSON>"}}}, "hello": {"description": "Placeholder example", "message": "Hello $1"}}