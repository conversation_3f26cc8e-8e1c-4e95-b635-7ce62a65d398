{"name": "@extension/ui", "version": "0.4.2", "description": "chrome extension - ui components", "type": "module", "private": true, "sideEffects": true, "files": ["dist/**", "dist/global.css"], "types": "dist/index.d.ts", "main": "dist/index.js", "module": "dist/index.js", "scripts": {"clean:bundle": "<PERSON><PERSON><PERSON> dist", "clean:node_modules": "pnpm dlx rimraf node_modules", "clean:turbo": "rimraf .turbo", "clean": "pnpm clean:bundle && pnpm clean:node_modules && pnpm clean:turbo", "ready": "tsc -b && tsc-alias -p tsconfig.json", "lint": "eslint .", "lint:fix": "pnpm lint --fix", "prettier": "prettier . --write", "type-check": "tsc --noEmit"}, "dependencies": {"@extension/storage": "workspace:*", "@extension/shared": "workspace:*", "clsx": "^2.1.1", "tailwind-merge": "^2.4.0"}, "devDependencies": {"@extension/tsconfig": "workspace:*", "tsc-alias": "^1.8.10"}}