#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Documents/Tools/mcp-sse-proxy/node_modules/.pnpm/@srbhptl39+mcp-superassistant-proxy@0.0.11/node_modules/@srbhptl39/mcp-superassistant-proxy/dist/node_modules:/Users/<USER>/Documents/Tools/mcp-sse-proxy/node_modules/.pnpm/@srbhptl39+mcp-superassistant-proxy@0.0.11/node_modules/@srbhptl39/mcp-superassistant-proxy/node_modules:/Users/<USER>/Documents/Tools/mcp-sse-proxy/node_modules/.pnpm/@srbhptl39+mcp-superassistant-proxy@0.0.11/node_modules/@srbhptl39/node_modules:/Users/<USER>/Documents/Tools/mcp-sse-proxy/node_modules/.pnpm/@srbhptl39+mcp-superassistant-proxy@0.0.11/node_modules:/Users/<USER>/Documents/Tools/mcp-sse-proxy/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Documents/Tools/mcp-sse-proxy/node_modules/.pnpm/@srbhptl39+mcp-superassistant-proxy@0.0.11/node_modules/@srbhptl39/mcp-superassistant-proxy/dist/node_modules:/Users/<USER>/Documents/Tools/mcp-sse-proxy/node_modules/.pnpm/@srbhptl39+mcp-superassistant-proxy@0.0.11/node_modules/@srbhptl39/mcp-superassistant-proxy/node_modules:/Users/<USER>/Documents/Tools/mcp-sse-proxy/node_modules/.pnpm/@srbhptl39+mcp-superassistant-proxy@0.0.11/node_modules/@srbhptl39/node_modules:/Users/<USER>/Documents/Tools/mcp-sse-proxy/node_modules/.pnpm/@srbhptl39+mcp-superassistant-proxy@0.0.11/node_modules:/Users/<USER>/Documents/Tools/mcp-sse-proxy/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../dist/index.js" "$@"
else
  exec node  "$basedir/../../dist/index.js" "$@"
fi
