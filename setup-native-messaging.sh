#!/bin/bash

# Setup Native Messaging for MCP SuperAssistant Proxy
# This script sets up native messaging for development

set -e

echo "🔧 Setting up Native Messaging for MCP SuperAssistant Proxy..."

# Detect OS
OS="$(uname -s)"
case "${OS}" in
    Linux*)     MACHINE=Linux;;
    Darwin*)    MACHINE=Mac;;
    CYGWIN*)    MACHINE=Cygwin;;
    MINGW*)     MACHINE=MinGw;;
    *)          MACHINE="UNKNOWN:${OS}"
esac

echo "📱 Detected OS: $MACHINE"

# Get the absolute path to the proxy executable
PROXY_PATH="$(pwd)/dist/index.js"
if [ ! -f "$PROXY_PATH" ]; then
    echo "❌ Error: Proxy executable not found at $PROXY_PATH"
    echo "Please run 'npm run build' first to build the proxy"
    exit 1
fi

echo "📍 Using proxy path: $PROXY_PATH"

# Create a wrapper script that calls node with the proxy
WRAPPER_SCRIPT="/tmp/mcp-proxy-wrapper"
cat > "$WRAPPER_SCRIPT" << EOF
#!/bin/bash
exec node "$PROXY_PATH" --config "$PROXY_PATH/../config-native-messaging.json" --nativeMessaging "\$@"
EOF
chmod +x "$WRAPPER_SCRIPT"

echo "📝 Created wrapper script at: $WRAPPER_SCRIPT"

# Create the native messaging manifest
MANIFEST_CONTENT='{
  "name": "com.yourcompany.mcp_proxy",
  "description": "MCP SuperAssistant Proxy Native Messaging Host",
  "path": "'$WRAPPER_SCRIPT'",
  "type": "stdio",
  "allowed_origins": [
    "chrome-extension://kngiafgkdnlkgmefdafaibkibegkcaef/"
  ]
}'

# Determine the correct manifest directory based on OS
if [ "$MACHINE" = "Mac" ]; then
    # macOS Chrome
    MANIFEST_DIR="$HOME/Library/Application Support/Google/Chrome/NativeMessagingHosts"
    mkdir -p "$MANIFEST_DIR"
    MANIFEST_FILE="$MANIFEST_DIR/com.yourcompany.mcp_proxy.json"
elif [ "$MACHINE" = "Linux" ]; then
    # Linux Chrome
    MANIFEST_DIR="$HOME/.config/google-chrome/NativeMessagingHosts"
    mkdir -p "$MANIFEST_DIR"
    MANIFEST_FILE="$MANIFEST_DIR/com.yourcompany.mcp_proxy.json"
else
    echo "❌ Unsupported OS: $MACHINE"
    echo "Please manually install the native messaging manifest"
    echo "Manifest content:"
    echo "$MANIFEST_CONTENT"
    exit 1
fi

# Write the manifest file
echo "$MANIFEST_CONTENT" > "$MANIFEST_FILE"

echo "✅ Native messaging manifest installed at: $MANIFEST_FILE"
echo ""
echo "🎉 Setup complete! You can now:"
echo "1. Load the Chrome extension from: $(pwd)/../dist"
echo "2. Start the proxy with: npm start -- --config config.json --nativeMessaging"
echo "3. The extension will automatically use native messaging as the primary transport"
echo ""
echo "🔍 To verify the setup:"
echo "- Check Chrome extension console for 'Native Messaging Transport' logs"
echo "- The proxy should show 'Starting MCP SuperAssistant Proxy in native messaging mode...'"
