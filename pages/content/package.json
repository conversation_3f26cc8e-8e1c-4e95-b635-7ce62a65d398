{"name": "@extension/content-script", "version": "0.4.2", "description": "chrome extension - content script", "type": "module", "private": true, "sideEffects": true, "files": ["dist/**"], "scripts": {"clean:node_modules": "pnpm dlx rimraf node_modules", "clean:turbo": "rimraf .turbo", "clean": "pnpm clean:turbo && pnpm clean:node_modules", "build": "vite build", "dev": "vite build --mode development", "lint": "eslint .", "lint:fix": "pnpm lint --fix", "prettier": "prettier . --write --ignore-path ../../.prettierignore", "type-check": "tsc --noEmit"}, "dependencies": {"@extension/env": "workspace:*", "@extension/shared": "workspace:*", "@extension/storage": "workspace:*", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-icons": "^1.3.2", "ajv": "^8.17.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "fast-xml-parser": "^5.0.9", "lucide-react": "^0.477.0", "react": "19.1.0", "react-dom": "19.1.0", "shadcn-ui": "^0.9.5", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@extension/hmr": "workspace:*", "@extension/tsconfig": "workspace:*", "@extension/vite-config": "workspace:*", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@types/webextension-polyfill": "^0.12.3", "autoprefixer": "^10.4.20", "postcss": "^8.5.2", "tailwindcss": "^3.4.17"}}