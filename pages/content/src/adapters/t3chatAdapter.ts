/**
 * T3 Chat Adapter
 *
 * This file implements the site adapter for t3.chat
 */

import { BaseAdapter } from './common';
import { logMessage } from '../utils/helpers';
import {
  insertToolResultToChatInput,
  attachFileToChatInput,
  submitChatInput,
} from '../components/websites/t3chat/index';
import { SidebarManager } from '../components/sidebar';
import { initT3ChatComponents } from './adaptercomponents';

export class T3ChatAdapter extends BaseAdapter {
  name = 'T3ChatAdapter';
  hostname = ['t3.chat']; // t3.chat
  // URL patterns to only activate on specific paths
  urlPatterns = [
    /https?:\/\/(?:www\.)?t3\.chat/, // Any t3.chat URL
  ];

  // Properties to track navigation
  private lastUrl: string = '';
  private urlCheckInterval: number | null = null;

  constructor() {
    super();
    // Create the sidebar manager instance
    this.sidebarManager = SidebarManager.getInstance('t3chat');
    logMessage('Created T3 Chat sidebar manager instance');
  }

  protected initializeSidebarManager(): void {
    this.sidebarManager.initialize();
  }

  protected initializeObserver(forceReset: boolean = false): void {
    // Check the current URL immediately
    this.checkCurrentUrl();

    initT3ChatComponents();

    // Start URL checking to handle navigation within T3 Chat
    if (!this.urlCheckInterval) {
      this.lastUrl = window.location.href;
      this.urlCheckInterval = window.setInterval(() => {
        const currentUrl = window.location.href;

        if (currentUrl !== this.lastUrl) {
          logMessage(`URL changed from ${this.lastUrl} to ${currentUrl}`);
          this.lastUrl = currentUrl;

          initT3ChatComponents();

          // Check if we should show or hide the sidebar based on URL
          this.checkCurrentUrl();
        }
      }, 1000); // Check every second
    }
  }

  /**
   * Clean up resources when the adapter is no longer needed
   */
  cleanup(): void {
    logMessage('Cleaning up T3 Chat adapter');

    // Clear interval for URL checking
    if (this.urlCheckInterval) {
      window.clearInterval(this.urlCheckInterval);
      this.urlCheckInterval = null;
    }

    // Call the parent cleanup method
    super.cleanup();
  }

  /**
   * Insert text into the T3 Chat input field
   * @param text Text to insert
   */
  insertTextIntoInput(text: string): void {
    insertToolResultToChatInput(text);
    logMessage(`Inserted text into T3 Chat input: ${text.substring(0, 20)}...`);
  }

  /**
   * Trigger submission of the T3 Chat input form
   */
  triggerSubmission(): void {
    submitChatInput();
    logMessage('Triggered T3 Chat form submission');
  }

  /**
   * Check if T3 Chat supports file upload
   * @returns true if file upload is supported
   */
  supportsFileUpload(): boolean {
    return true;
  }

  /**
   * Attach a file to the T3 Chat input
   * @param file The file to attach
   * @returns Promise that resolves to true if successful
   */
  async attachFile(file: File): Promise<boolean> {
    try {
      const result = await attachFileToChatInput(file);
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logMessage(`Error in adapter when attaching file to T3 Chat input: ${errorMessage}`);
      console.error('Error in adapter when attaching file to T3 Chat input:', error);
      return false;
    }
  }

  /**
   * Force a full document scan for tool commands
   * This is useful when we suspect tool commands might have been missed
   */
  public forceFullScan(): void {
    logMessage('Forcing full document scan for T3 Chat');
  }

  /**
   * Check the current URL and show/hide sidebar accordingly
   */
  private checkCurrentUrl(): void {
    const currentUrl = window.location.href;
    logMessage(`Checking current T3 Chat URL: ${currentUrl}`);

    // For T3 Chat, we want to show the sidebar only on URLs that match our patterns
    const isValidUrl = this.urlPatterns.some(pattern => pattern.test(currentUrl));

    if (isValidUrl) {
      // Show sidebar for valid URLs
      if (this.sidebarManager && !this.sidebarManager.getIsVisible()) {
        logMessage('Showing sidebar for T3 Chat URL');
        this.sidebarManager.showWithToolOutputs();
      }
    } else {
      // Hide sidebar for invalid URLs
      if (this.sidebarManager && this.sidebarManager.getIsVisible()) {
        logMessage('Hiding sidebar for non-T3 Chat URL');
        this.sidebarManager.hide();
      }
    }
  }
}
