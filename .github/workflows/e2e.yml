name: Run E2E Tests

on:
  push:
    branches: [ main, dev ]
  pull_request:
    branches: [ main, dev ]

jobs:
  chrome:
    name: E2E tests for Chrome
    runs-on: ubuntu-latest
    env:
      CEB_CI: true
    steps:
      - uses: actions/checkout@v4
      - uses: pnpm/action-setup@v4
      - uses: actions/setup-node@v4
        with:
          node-version-file: '.nvmrc'
          cache: pnpm
      - run: pnpm install --frozen-lockfile --prefer-offline
      - run: pnpm e2e

  firefox:
    name: E2E tests for Firefox
    runs-on: ubuntu-latest
    env:
      CEB_CI: true
    steps:
      - uses: actions/checkout@v4
      - uses: pnpm/action-setup@v4
      - uses: actions/setup-node@v4
        with:
          node-version-file: '.nvmrc'
          cache: pnpm
      - run: pnpm install --frozen-lockfile --prefer-offline
      - run: pnpm e2e:firefox
