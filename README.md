# MCP SuperAssistant Proxy

MCP SuperAssistant Proxy lets you run multiple **MCP stdio-based** and **SSE-based** servers and expose them through a single endpoint. This allows MCP SuperAssistant and other tools to connect to multiple remote MCP servers and tools via a unified proxy.

**🚀 Features:**
- **Native Messaging Support** - Direct Chrome extension communication (bypasses networking issues)
- **Cross-Platform** - Works on macOS, Linux, and Windows
- **Multiple Transport Methods** - SSE, HTTP, and Native Messaging
- **Auto-Setup** - Automatically configures native messaging during installation

## Installation & Usage

### For Chrome Extension (Native Messaging - Recommended)

```bash
# One-time setup: Install and configure native messaging
npm install -g @leeroy/mcp-superassistant-proxy

# That's it! Chrome extension will automatically launch the proxy when needed
# No manual npx commands required
```

### For Web/HTTP Clients

```bash
# Traditional HTTP/SSE server mode
npx -y @leeroy/mcp-superassistant-proxy@latest --config config.json --port 3006
```

### CLI Options

- `--config, -c <path>`: **(required)** Path to a JSON configuration file (see below)
- `--nativeMessaging`: Run in native messaging mode for Chrome extensions (recommended)
- `--port <number>`: Port to run the HTTP server on (default: `3006`, ignored in native messaging mode)
- `--baseUrl <url>`: Base URL for SSE clients (default: `http://localhost:<port>`)
- `--ssePath <path>`: Path for SSE subscriptions (default: `/sse`)
- `--messagePath <path>`: Path for SSE messages (default: `/message`)
- `--logLevel <info|none>`: Set logging level (default: `info`)
- `--cors`: Enable CORS (default: `true`)
- `--healthEndpoint <path>`: One or more endpoints returning `"ok"` (can be used multiple times)
- `--timeout <ms>`: Connection timeout in milliseconds (default: `30000`)

## Configuration File

The configuration file is a JSON file specifying which MCP servers to connect to. Each server can be either a stdio-based server (run as a subprocess) or an SSE-based server (remote URL).

### Example `config.json`

```json
{
  "mcpServers": {
    "notion": {
      "command": "npx",
      "args": ["-y", "@suekou/mcp-notion-server"],
      "env": {
        "NOTION_API_TOKEN": "<your_notion_token_here>"
      }
    },
    "gmail": {
      "url": "https://mcp.composio.dev/gmail/xxxx"
    },
    "youtube-subtitle-downloader": {
      "command": "bun",
      "args": [
        "run",
        "/path/to/mcp-youtube/src/index.ts"
      ]
    },
    "desktop-commander": {
      "command": "npx",
      "args": ["-y", "@wonderwhy-er/desktop-commander"]
    },
    "iterm-mcp": {
      "command": "npx",
      "args": ["-y", "iterm-mcp"]
    }
  }
}
```

- Each key under `mcpServers` is a unique name for the server.
- For stdio-based servers, specify `command`, `args`, and optionally `env`.
- For SSE-based servers, specify `url`.

## Native Messaging Setup

When you install the package, native messaging is automatically configured for:
- **Chrome** - `~/Library/Application Support/Google/Chrome/NativeMessagingHosts/` (macOS)
- **Chromium** - `~/Library/Application Support/Chromium/NativeMessagingHosts/` (macOS)
- **Microsoft Edge** - `~/Library/Application Support/Microsoft Edge/NativeMessagingHosts/` (macOS)
- **Linux** - `~/.config/google-chrome/NativeMessagingHosts/` (and similar for other browsers)
- **Windows** - `%APPDATA%\Google\Chrome\User Data\NativeMessagingHosts\` (and similar)

The setup creates:
1. **Config file** - `~/.mcp-superassistant/config.json` (easily accessible)
2. **Wrapper script** - Calls the proxy with the config file and `--nativeMessaging` flag
3. **Manifest files** - Tells browsers where to find the native messaging host
4. **Fixed Extension ID** - `kngiafgkdnlkgmefdafaibkibegkcaef` for consistent communication

### Configuration

**Easy config management:**
```bash
# Show current config
mcp-config show

# Edit config file (opens in your default editor)
mcp-config edit

# Show config file path
mcp-config path
```

**Manual editing:**
Edit your MCP servers in: `~/.mcp-superassistant/config.json`

```json
{
  "mcpServers": {
    "filesystem": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-filesystem", "/tmp"],
      "env": {}
    },
    "your-custom-server": {
      "command": "node",
      "args": ["/path/to/your/server.js"],
      "env": {
        "API_KEY": "your-api-key"
      }
    }
  }
}
```

### Manual Uninstall
```bash
npm uninstall -g @leeroy/mcp-superassistant-proxy
# Automatically removes native messaging files
```

## HTTP/SSE Endpoints

When running in HTTP mode (`--port`), the proxy exposes:
- **SSE endpoint**: `GET http://localhost:<port>/sse`
- **POST messages**: `POST http://localhost:<port>/message`

(You can customize the paths with `--ssePath` and `--messagePath`.)

## Example

1. **Create a config file** (e.g., `config.json`) as shown above.
2. **Run MCP SuperAssistant Proxy**:
   ```bash
   npx -y @srbhptl39/mcp-superassistant-proxy@latest --config config.json --port 3006
   ```

## Why MCP?

[Model Context Protocol](https://spec.modelcontextprotocol.io/) standardizes how AI tools exchange data. If your MCP server only speaks stdio, MCP SuperAssistant Proxy exposes an SSE-based interface so remote clients (and tools like MCP Inspector or Claude Desktop) can connect without extra server changes. It also allows you to aggregate multiple MCP servers behind a single endpoint.

## Advanced Configuration

MCP SuperAssistant Proxy is designed with modularity in mind:
- Supports both stdio and SSE MCP servers in one config.
- Automatically derives the JSON‑RPC version from incoming requests, ensuring future compatibility.
- Package information (name and version) is retransmitted where possible.
- Stdio-to-SSE mode uses standard logs and SSE-to-Stdio mode logs via stderr (as otherwise it would prevent stdio functionality).
- The SSE-to-SSE mode provides automatic reconnection with backoff if the remote server connection is lost.
- Health endpoints can be added for monitoring.

---

For more details, see the [Model Context Protocol documentation](https://modelcontextprotocol.io/).

