#!/usr/bin/env node

import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { writeFileSync, mkdirSync, chmodSync } from 'fs';
import { homedir, platform } from 'os';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Get the path to the installed package
const packageRoot = join(__dirname, '..');
const executablePath = join(packageRoot, 'dist', 'index.js');

console.log('🔧 Setting up Native Messaging for MCP SuperAssistant Proxy...');
console.log(`📱 Detected OS: ${platform()}`);
console.log(`📍 Package installed at: ${packageRoot}`);

// Create wrapper script that will be called by Chrome
const wrapperScript = `#!/bin/bash
exec node "${executablePath}" --nativeMessaging "$@"
`;

// Determine the correct directories based on OS
let manifestDir;
let wrapperPath;

switch (platform()) {
  case 'darwin': // macOS
    manifestDir = join(homedir(), 'Library', 'Application Support', 'Google', 'Chrome', 'NativeMessagingHosts');
    wrapperPath = join(packageRoot, 'mcp-proxy-native-wrapper');
    break;
  case 'linux':
    manifestDir = join(homedir(), '.config', 'google-chrome', 'NativeMessagingHosts');
    wrapperPath = join(packageRoot, 'mcp-proxy-native-wrapper');
    break;
  case 'win32':
    console.log('❌ Windows native messaging setup not yet implemented');
    console.log('Please manually install the native messaging manifest');
    process.exit(0);
  default:
    console.log(`❌ Unsupported OS: ${platform()}`);
    process.exit(1);
}

try {
  // Create the manifest directory if it doesn't exist
  mkdirSync(manifestDir, { recursive: true });

  // Write the wrapper script
  writeFileSync(wrapperPath, wrapperScript);
  chmodSync(wrapperPath, 0o755);

  // Create the native messaging manifest
  const manifest = {
    name: 'com.yourcompany.mcp_proxy',
    description: 'MCP SuperAssistant Proxy Native Messaging Host',
    path: wrapperPath,
    type: 'stdio',
    allowed_origins: [
      'chrome-extension://kngiafgkdnlkgmefdafaibkibegkcaef/'
    ]
  };

  const manifestPath = join(manifestDir, 'com.yourcompany.mcp_proxy.json');
  writeFileSync(manifestPath, JSON.stringify(manifest, null, 2));

  console.log('✅ Native messaging setup complete!');
  console.log(`📝 Wrapper script: ${wrapperPath}`);
  console.log(`📄 Manifest file: ${manifestPath}`);
  console.log('');
  console.log('🎉 You can now use native messaging with the Chrome extension!');
  console.log('');
  console.log('📋 Usage:');
  console.log('  # Run in native messaging mode:');
  console.log('  npx @leeroy/mcp-superassistant-proxy --config config.json --nativeMessaging');
  console.log('');
  console.log('  # Run in HTTP server mode:');
  console.log('  npx @leeroy/mcp-superassistant-proxy --config config.json --port 3006');

} catch (error) {
  console.error('❌ Failed to set up native messaging:', error.message);
  console.log('');
  console.log('You can still use the proxy in HTTP mode:');
  console.log('npx @leeroy/mcp-superassistant-proxy --config config.json --port 3006');
  process.exit(0); // Don't fail the install
}
