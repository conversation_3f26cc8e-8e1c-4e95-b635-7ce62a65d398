#!/usr/bin/env node

import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { writeFileSync, mkdirSync, chmodSync, existsSync } from 'fs';
import { homedir, platform } from 'os';
import { execSync } from 'child_process';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Get the path to the installed package
const packageRoot = join(__dirname, '..');
const executablePath = join(packageRoot, 'dist', 'index.js');

console.log('🔧 Setting up Native Messaging for MCP SuperAssistant Proxy...');
console.log(`📱 Detected OS: ${platform()}`);
console.log(`📍 Package installed at: ${packageRoot}`);

// Determine the correct directories and wrapper script based on OS
let manifestDirs = [];
let wrapperPath;
let wrapperScript;

switch (platform()) {
  case 'darwin': // macOS
    manifestDirs = [
      join(homedir(), 'Library', 'Application Support', 'Google', 'Chrome', 'NativeMessagingHosts'),
      join(homedir(), 'Library', 'Application Support', 'Chromium', 'NativeMessagingHosts'),
      join(homedir(), 'Library', 'Application Support', 'Microsoft Edge', 'NativeMessagingHosts')
    ];
    // Use a more permanent location in the user's Library for the wrapper
    wrapperPath = join(homedir(), 'Library', 'Application Support', 'mcp-superassistant-proxy', 'mcp-proxy-native-wrapper');
    // But use a more accessible location for the config
    const macConfigPath = join(homedir(), '.mcp-superassistant', 'config.json');
    wrapperScript = `#!/bin/bash
# MCP SuperAssistant Proxy Native Messaging Wrapper
# Config file: ${macConfigPath}
exec node "${executablePath}" --config "${macConfigPath}" --nativeMessaging "$@"
`;
    break;

  case 'linux':
    manifestDirs = [
      join(homedir(), '.config', 'google-chrome', 'NativeMessagingHosts'),
      join(homedir(), '.config', 'chromium', 'NativeMessagingHosts'),
      join(homedir(), '.config', 'microsoft-edge', 'NativeMessagingHosts')
    ];
    // Use XDG-compliant location for wrapper
    const xdgDataHome = process.env.XDG_DATA_HOME || join(homedir(), '.local', 'share');
    wrapperPath = join(xdgDataHome, 'mcp-superassistant-proxy', 'mcp-proxy-native-wrapper');
    // But use a more accessible location for the config
    const linuxConfigPath = join(homedir(), '.mcp-superassistant', 'config.json');
    wrapperScript = `#!/bin/bash
# MCP SuperAssistant Proxy Native Messaging Wrapper
# Config file: ${linuxConfigPath}
exec node "${executablePath}" --config "${linuxConfigPath}" --nativeMessaging "$@"
`;
    break;

  case 'win32':
    // Windows paths for different browsers
    const appData = process.env.APPDATA || join(homedir(), 'AppData', 'Roaming');
    const localAppData = process.env.LOCALAPPDATA || join(homedir(), 'AppData', 'Local');
    manifestDirs = [
      join(appData, 'Google', 'Chrome', 'User Data', 'NativeMessagingHosts'),
      join(localAppData, 'Google', 'Chrome', 'User Data', 'NativeMessagingHosts'),
      join(appData, 'Microsoft', 'Edge', 'User Data', 'NativeMessagingHosts'),
      join(localAppData, 'Microsoft', 'Edge', 'User Data', 'NativeMessagingHosts')
    ];
    // Use proper Windows application data location for wrapper
    wrapperPath = join(appData, 'mcp-superassistant-proxy', 'mcp-proxy-native-wrapper.bat');
    // But use a more accessible location for the config
    const winConfigPath = join(homedir(), '.mcp-superassistant', 'config.json');
    wrapperScript = `@echo off
REM MCP SuperAssistant Proxy Native Messaging Wrapper
REM Config file: ${winConfigPath}
node "${executablePath}" --config "${winConfigPath}" --nativeMessaging %*
`;
    break;

  default:
    console.log(`❌ Unsupported OS: ${platform()}`);
    console.log('Supported platforms: macOS (darwin), Linux, Windows (win32)');
    process.exit(1);
}

try {
  // Create the wrapper script directory and write the script
  const wrapperDir = dirname(wrapperPath);
  mkdirSync(wrapperDir, { recursive: true });
  writeFileSync(wrapperPath, wrapperScript);

  // Set executable permissions (not needed on Windows)
  if (platform() !== 'win32') {
    chmodSync(wrapperPath, 0o755);
  }

  console.log(`📝 Wrapper script created: ${wrapperPath}`);

  // Create a sample config file if it doesn't exist
  const configPath = join(homedir(), '.mcp-superassistant', 'config.json');
  if (!existsSync(configPath)) {
    // Create the config directory
    mkdirSync(dirname(configPath), { recursive: true });

    const sampleConfig = {
      mcpServers: {
      }
    };

    writeFileSync(configPath, JSON.stringify(sampleConfig, null, 2));
    console.log(`📄 Sample config created: ${configPath}`);
  } else {
    console.log(`📄 Config file already exists: ${configPath}`);
  }

  // Create the native messaging manifest
  const manifest = {
    name: 'ai.mcpsuperassistant.mcp_proxy',
    description: 'MCP SuperAssistant Proxy Native Messaging Host',
    path: wrapperPath,
    type: 'stdio',
    allowed_origins: [
      'chrome-extension://kngiafgkdnlkgmefdafaibkibegkcaef/'
    ]
  };

  // Install manifest for all detected browsers
  const installedManifests = [];
  let hasErrors = false;

  for (const manifestDir of manifestDirs) {
    try {
      // Create the manifest directory if it doesn't exist
      mkdirSync(manifestDir, { recursive: true });

      const manifestPath = join(manifestDir, 'ai.mcpsuperassistant.mcp_proxy.json');
      writeFileSync(manifestPath, JSON.stringify(manifest, null, 2));

      installedManifests.push(manifestPath);
      console.log(`📄 Manifest installed: ${manifestPath}`);
    } catch (dirError) {
      console.warn(`⚠️  Could not install manifest in ${manifestDir}: ${dirError.message}`);
      hasErrors = true;
    }
  }

  if (installedManifests.length === 0) {
    throw new Error('Could not install native messaging manifest in any browser directory');
  }

  console.log('');
  console.log('✅ Native messaging setup complete!');
  console.log(`📝 Wrapper script: ${wrapperPath}`);
  console.log(`📄 Manifests installed: ${installedManifests.length}`);

  if (hasErrors) {
    console.log('⚠️  Some browser directories were not accessible (this is normal if those browsers are not installed)');
  }

  console.log('');
  console.log('🎉 Native messaging is now ready for Chrome extensions!');
  console.log('');
  console.log('📋 How it works:');
  console.log('  1. Chrome extension connects → Chrome launches the wrapper script');
  console.log('  2. Wrapper script runs the proxy with the config file');
  console.log('  3. No manual npx command needed!');
  console.log('');
  console.log('📝 To customize your MCP servers:');
  console.log(`  Edit: ${configPath}`);
  console.log('');
  console.log('🔧 For manual testing:');
  console.log('  # Test HTTP server mode:');
  console.log('  npx @leeroy/mcp-superassistant-proxy --config config.json --port 3006');
  console.log('');
  console.log('🔍 Supported browsers: Chrome, Chromium, Microsoft Edge');

} catch (error) {
  console.error('❌ Failed to set up native messaging:', error.message);
  console.log('');
  console.log('This is not a critical error. You can still use the proxy in HTTP mode:');
  console.log('npx @leeroy/mcp-superassistant-proxy --config config.json --port 3006');
  console.log('');
  console.log('For manual native messaging setup, see: https://developer.chrome.com/docs/extensions/develop/concepts/native-messaging');
  process.exit(0); // Don't fail the install
}
