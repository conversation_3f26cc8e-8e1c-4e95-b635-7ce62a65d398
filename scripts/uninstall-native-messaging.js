#!/usr/bin/env node

import { join } from 'path';
import { unlinkSync, rmSync, existsSync } from 'fs';
import { homedir, platform } from 'os';

console.log('🗑️  Uninstalling Native Messaging for MCP SuperAssistant Proxy...');
console.log(`📱 Detected OS: ${platform()}`);

// Determine the correct directories based on OS
let manifestDirs = [];
let wrapperPath;

switch (platform()) {
  case 'darwin': // macOS
    manifestDirs = [
      join(homedir(), 'Library', 'Application Support', 'Google', 'Chrome', 'NativeMessagingHosts'),
      join(homedir(), 'Library', 'Application Support', 'Chromium', 'NativeMessagingHosts'),
      join(homedir(), 'Library', 'Application Support', 'Microsoft Edge', 'NativeMessagingHosts')
    ];
    wrapperPath = join(homedir(), 'Library', 'Application Support', 'mcp-superassistant-proxy');
    break;
    
  case 'linux':
    manifestDirs = [
      join(homedir(), '.config', 'google-chrome', 'NativeMessagingHosts'),
      join(homedir(), '.config', 'chromium', 'NativeMessagingHosts'),
      join(homedir(), '.config', 'microsoft-edge', 'NativeMessagingHosts')
    ];
    const xdgDataHome = process.env.XDG_DATA_HOME || join(homedir(), '.local', 'share');
    wrapperPath = join(xdgDataHome, 'mcp-superassistant-proxy');
    break;
    
  case 'win32':
    const appData = process.env.APPDATA || join(homedir(), 'AppData', 'Roaming');
    const localAppData = process.env.LOCALAPPDATA || join(homedir(), 'AppData', 'Local');
    manifestDirs = [
      join(appData, 'Google', 'Chrome', 'User Data', 'NativeMessagingHosts'),
      join(localAppData, 'Google', 'Chrome', 'User Data', 'NativeMessagingHosts'),
      join(appData, 'Microsoft', 'Edge', 'User Data', 'NativeMessagingHosts'),
      join(localAppData, 'Microsoft', 'Edge', 'User Data', 'NativeMessagingHosts')
    ];
    wrapperPath = join(appData, 'mcp-superassistant-proxy');
    break;
    
  default:
    console.log(`❌ Unsupported OS: ${platform()}`);
    process.exit(1);
}

let removedCount = 0;

// Remove manifest files
for (const manifestDir of manifestDirs) {
  const manifestPath = join(manifestDir, 'com.yourcompany.mcp_proxy.json');
  
  if (existsSync(manifestPath)) {
    try {
      unlinkSync(manifestPath);
      console.log(`🗑️  Removed manifest: ${manifestPath}`);
      removedCount++;
    } catch (error) {
      console.warn(`⚠️  Could not remove manifest ${manifestPath}: ${error.message}`);
    }
  }
}

// Remove wrapper script directory
if (existsSync(wrapperPath)) {
  try {
    rmSync(wrapperPath, { recursive: true, force: true });
    console.log(`🗑️  Removed wrapper directory: ${wrapperPath}`);
    removedCount++;
  } catch (error) {
    console.warn(`⚠️  Could not remove wrapper directory ${wrapperPath}: ${error.message}`);
  }
}

// Remove config directory (ask user first since it may contain custom configs)
const configDir = join(homedir(), '.mcp-superassistant');
if (existsSync(configDir)) {
  console.log('');
  console.log(`📁 Config directory found: ${configDir}`);
  console.log('⚠️  This directory may contain your custom MCP server configurations.');
  console.log('💡 You may want to back up your config before removing it.');
  console.log('🗑️  To remove it manually: rm -rf ~/.mcp-superassistant');
}

if (removedCount > 0) {
  console.log('');
  console.log('✅ Native messaging uninstall complete!');
  console.log(`🗑️  Removed ${removedCount} items`);
} else {
  console.log('');
  console.log('ℹ️  No native messaging files found to remove');
}

console.log('');
console.log('🎉 MCP SuperAssistant Proxy native messaging has been uninstalled');
