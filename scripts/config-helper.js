#!/usr/bin/env node

import { join } from 'path';
import { existsSync, readFileSync } from 'fs';
import { homedir } from 'os';
import { execSync } from 'child_process';

const configPath = join(homedir(), '.mcp-superassistant', 'config.json');

const command = process.argv[2];

switch (command) {
  case 'path':
    console.log(configPath);
    break;
    
  case 'edit':
    if (!existsSync(configPath)) {
      console.log('❌ Config file not found. Run "npm install -g @leeroy/mcp-superassistant-proxy" first.');
      process.exit(1);
    }
    
    // Try to open with common editors
    const editors = ['code', 'nano', 'vim', 'open'];
    let opened = false;
    
    for (const editor of editors) {
      try {
        if (editor === 'open') {
          // macOS default app
          execSync(`open "${configPath}"`, { stdio: 'inherit' });
        } else {
          execSync(`which ${editor}`, { stdio: 'ignore' });
          execSync(`${editor} "${configPath}"`, { stdio: 'inherit' });
        }
        opened = true;
        break;
      } catch (error) {
        // Editor not found, try next
      }
    }
    
    if (!opened) {
      console.log(`📝 Please edit: ${configPath}`);
    }
    break;
    
  case 'show':
    if (!existsSync(configPath)) {
      console.log('❌ Config file not found. Run "npm install -g @leeroy/mcp-superassistant-proxy" first.');
      process.exit(1);
    }
    
    try {
      const config = JSON.parse(readFileSync(configPath, 'utf8'));
      console.log('📄 Current MCP SuperAssistant Proxy configuration:');
      console.log('');
      console.log(JSON.stringify(config, null, 2));
    } catch (error) {
      console.log('❌ Error reading config file:', error.message);
      process.exit(1);
    }
    break;
    
  default:
    console.log('🔧 MCP SuperAssistant Proxy Config Helper');
    console.log('');
    console.log('Usage:');
    console.log('  mcp-superassistant-proxy config path   # Show config file path');
    console.log('  mcp-superassistant-proxy config edit   # Edit config file');
    console.log('  mcp-superassistant-proxy config show   # Show current config');
    console.log('');
    console.log(`📍 Config location: ${configPath}`);
    break;
}
