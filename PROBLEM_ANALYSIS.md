Okay, this is a great set of files and context. It allows for a much deeper dive.

Let's break this down systematically.

**Primary Goal:** Achieve a stable connection between the Chrome extension and the MCP proxy, and ensure correct path handling for tools like `mcp-server-filesystem`.

**Analysis Strategy:**

1.  **Proxy Comparison (Recap & Refine):** Briefly reiterate key differences relevant to stability and functionality.
2.  **Chrome Extension Deep Dive:**
    *   How does it manage connections to the proxy? (`officialmcpclient.ts`, `mcpinterfaceToContentScript.ts`)
    *   How does it handle errors and disconnections?
    *   How does it decide the server is "disconnected"?
    *   Are there heartbeats *from the extension to the proxy server*?
3.  **Interaction Analysis:** Where could the "Server Disconnected" issue originate? Is it the proxy dropping, the extension misinterpreting, or the communication link failing?
4.  **Path Handling Analysis:** How does the extension/sidebar prepare tool arguments, especially for filesystem tools? How does the proxy handle them?
5.  **Recommendations & Proposed Proxy Enhancement:** Based on the analysis, suggest a path forward. This will likely involve enhancing the *original* proxy.

---

**1. Proxy Comparison (Recap Relevant to Current Issues)**

*   **Original Proxy (`original_superassistant_proxy`):**
    *   **Pro:** Supports both StreamableHTTP (modern) and SSE (legacy) for clients connecting *to it*. Good for compatibility.
    *   **Pro:** Correctly uses `StreamableHTTPClientTransport` and `SSEClientTransport` (via `connectWithBackwardsCompatibility`) when acting as a client to *backend* MCP servers.
    *   **Con (Likely for Disconnection):** Lacks explicit heartbeats on its *own server-side transports* (`/mcp` and `/sse`). Idle TCP connections might be dropped by intermediaries or OS.
    *   **Con:** Basic logging.
*   **"Enhanced" Proxy (`mcp_sse_proxy`):**
    *   **Pro:** Adds server-side heartbeats to *its SSE transport*. This is good for SSE client stability.
    *   **Pro:** Better logging.
    *   **Con:** *Only* exposes an SSE transport to clients. This is a regression from MCP spec.
    *   **Con:** *Only* uses SSE to connect to backend MCP servers (removed StreamableHTTP client capability).
    *   **Con (Potential Path Issue/Complexity):** Uses `StdioServerTransport` in an unconventional way for stdio backends; `shell: true` on spawn.
    *   **Con:** Simpler child process termination.

The path issue you mentioned with the "enhanced" proxy using `mcp-server-filesystem` is most likely due to one of these:
    *   **CWD of the proxy:** If the proxy is run from a different directory, relative paths interpreted by `mcp-server-filesystem` (if its root is relative) will change.
    *   **Configuration in `mcpconfig.json`:** The `args` for `mcp-server-filesystem` might specify a relative root path. Using an absolute path for the filesystem server's root in `mcpconfig.json` is generally more robust.
    *   **`shell: true`:** Could affect how commands with special characters are interpreted, but less likely the primary cause for simple path args.
    *   The proxy itself (either version) typically doesn't modify the *values* of arguments like paths passed to tools; it just forwards the JSON-RPC request.

---

**2. Chrome Extension Deep Dive (Connection Management & Error Handling)**

Let's look at `officialmcpclient.ts` (PersistentMcpClient) and `mcpinterfaceToContentScript.ts` (McpInterface).

**`PersistentMcpClient` (`officialmcpclient.ts`):**

*   **Singleton:** Manages a single, persistent connection.
*   **Connection Logic (`connect` & `createConnection`):**
    *   Uses `SSEClientTransport` exclusively to connect to the proxy server URL. (StreamableHTTP client is commented out).
    *   Has a 10-second connection timeout (`connectionTimeout`).
    *   If `consecutiveFailures >= maxConsecutiveFailures` (3), it throws "Connection permanently failed".
    *   Resets `reconnectAttempts = 0` and `consecutiveFailures = 0` on successful connection.
*   **Disconnection (`disconnect`):**
    *   Calls `client.close()` and `transport.close()`.
    *   Sets `isConnected = false`, clears `client`, `transport`, `connectionPromise`.
*   **Reconnection (`scheduleReconnect` & `forceReconnect`):**
    *   `scheduleReconnect` explicitly states: "No automatic reconnection - all reconnection is user-driven only". This is a **critical point**. If the connection drops, it will *not* automatically try to reconnect beyond the initial `tryConnectToServer` attempts in the background script.
    *   `forceReconnect` is called by the UI/user. It resets failure counters and calls `connect()`.
*   **Connection Check (`ensureConnection`, `getConnectionStatus`, `checkConnectionStatus`):**
    *   `ensureConnection`: If not connected or last check is too old, it tries to `createConnection`.
    *   `getConnectionStatus`: Returns `this.isConnected`. If the last check was > 60s ago, it triggers a background `checkConnectionStatus()`.
    *   `checkConnectionStatus` (private): Relies on `this.client` existing. *It doesn't actively ping the server.* It assumes if `this.client` is present, the SDK's transport layer is managing the connection.
*   **Error Handling (`callTool`, `getPrimitives`, `isConnectionError`):**
    *   `isConnectionError` checks error messages for common network/connection failure strings.
    *   If a connection error is detected during `callTool` or `getPrimitives`, it sets `this.isConnected = false` and clears client/transport. **It does not automatically retry here.**
*   **Caching:** Caches primitives for 5 minutes (`primitivesMaxAge`).

**`McpInterface` (`mcpinterfaceToContentScript.ts`):**

*   **Singleton:** Manages communication between content scripts and the background script (where `PersistentMcpClient` lives).
*   **Ports:** Uses `chrome.runtime.Port` for communication with content scripts.
*   **Heartbeat (`startHeartbeat`, `handleMessage` for `HEARTBEAT`):**
    *   This heartbeat is between the *content script and the background script* to keep the `chrome.runtime.Port` alive and detect if a content script tab is closed or unresponsive.
    *   It sends a `HEARTBEAT` message to the background, which responds with `HEARTBEAT_RESPONSE`.
    *   If no response for `heartbeatTimeoutThreshold` (15s), it disconnects that specific port.
    *   **Crucially, this is NOT a heartbeat to the actual MCP proxy server.**
*   **Connection Check (`startConnectionCheck`, `checkServerConnection`):**
    *   `startConnectionCheck`: "Only do an initial check, no periodic checks." This aligns with `PersistentMcpClient`'s user-driven reconnect.
    *   `checkServerConnection`: Uses `checkMcpServerConnection()` from `officialmcpclient.ts`.
*   **Error Categorization (`categorizeError`):**
    *   Tries to distinguish between tool errors (e.g., "tool not found", "invalid arguments") and connection errors.
    *   If a connection error is detected, it updates `this.isConnected = false` and broadcasts. For tool errors, it doesn't change the overall connection status. This is good.
*   **Server URL Management:**
    *   Loads `mcpServerUrl` from `chrome.storage.local`.
    *   `handleUpdateServerConfig` saves the new URL and calls `forceReconnectToMcpServer`.

**Key Observations from Extension Code:**

1.  **No Client-to-Proxy Heartbeats:** The extension (specifically `PersistentMcpClient`) does *not* send any application-level heartbeats (e.g., a dummy MCP message) to the MCP proxy server to keep the TCP connection alive.
2.  **Limited Automatic Reconnection:** `PersistentMcpClient` and `McpInterface` are designed for primarily user-initiated reconnections after the initial phase. If the connection drops silently, the extension will reflect "Disconnected" and wait for user action.
3.  **Error Handling Distinction:** The attempt to categorize errors to differentiate between tool issues and actual connection problems is good. It prevents marking the server as "Disconnected" for a simple "Tool not found" error.
4.  **`checkMcpServerConnection` Relies on `PersistentMcpClient`'s State:** The function used by the UI to check the connection status ultimately relies on `PersistentMcpClient.getConnectionStatus()`, which is largely based on its internal `isConnected` flag. This flag is only set to `false` upon explicit errors or disconnects. It doesn't involve active probing for every status check unless a long time has passed.

---

**3. Interaction Analysis: "Server Disconnected" Issue**

Given the above:

*   The **original proxy** doesn't send heartbeats *from its server transport to the client (extension)*.
*   The **Chrome extension** doesn't send heartbeats *from its MCP client to the proxy server*.

This is a classic recipe for TCP connection drops due to inactivity. Firewalls, NAT routers, load balancers, or even the OS on either side can decide to close an idle TCP connection after a timeout (which can vary from minutes to hours).

When this happens:
1.  The TCP connection is severed silently.
2.  The extension's `PersistentMcpClient` still thinks `this.isConnected = true` and `this.client` is valid.
3.  The UI (`ServerStatus.tsx`) reflects "Connected".
4.  When the user tries an action (e.g., fetch tools, call a tool):
    *   `PersistentMcpClient.ensureConnection()` might think it's fine if `lastConnectionCheck` isn't too old.
    *   The actual `client.callTool()` or `client.listTools()` will attempt to send data over the dead TCP socket.
    *   This will result in a network error (e.g., connection reset, broken pipe).
    *   `PersistentMcpClient`'s error handler (or `McpInterface`'s `categorizeError`) will catch this, identify it as a connection error, set `this.isConnected = false`, and broadcast the status.
    *   The UI then updates to "Server Disconnected".
    *   The system now waits for the user to click "Reconnect".

**The "Server Disconnected" message is likely appearing *after* an attempted operation fails on a stale connection, not because of a proactive check.**

The `mcp_sse_proxy` (enhanced version) *does* add heartbeats from its SSE server endpoint *to its connected clients*. If the Chrome extension were purely an SSE client to *this specific proxy's SSE endpoint*, these heartbeats would help keep that specific SSE connection alive. However, the extension's `PersistentMcpClient` uses the `@modelcontextprotocol/sdk`'s `SSEClientTransport`, which will handle the SSE protocol's inherent keep-alive mechanisms if the server sends SSE comments (like `:heartbeat`). The original proxy's SSE transport doesn't explicitly send these comments.

---

**4. Path Handling Analysis (Briefly Revisited)**

*   **`pages/content/src/components/sidebar/Instructions/instructionGenerator.ts`:** This generates the system prompt. It includes placeholders like `$FUNCTION_NAME`, `$CALL_ID`, `$PARAMETER_NAME_1`, `$PARAMETER_VALUE`. The actual values are filled in by the AI or the user.
*   **Tool Execution:** When `mcpHandler.callTool(toolName, args, callback)` is invoked, the `args` object is passed.
    *   If the AI generates:
        ```xml
        <invoke name="filesystem.read_file" call_id="1">
        <parameter name="path">my_folder/my_document.txt</parameter>
        </invoke>
        ```
        The `args` passed to `filesystem.read_file` by the proxy will be `{"path": "my_folder/my_document.txt"}`.
*   **Proxy's Role:** The proxy (either version) forwards this JSON-RPC request largely as-is. It doesn't typically resolve or modify path strings within arguments.
*   **`mcp-server-filesystem`'s Role:**
    *   This server is configured in `mcpconfig.json`. It likely takes a *root directory* as one of its own startup arguments (e.g., `npx @modelcontextprotocol/server-filesystem /path/to/serve` or `npx @modelcontextprotocol/server-filesystem ./relative_data_folder`).
    *   When it receives `{"path": "my_folder/my_document.txt"}`, it interprets `"my_folder/my_document.txt"` *relative to its configured root directory*.
*   **Source of Path Issues with `mcp_sse_proxy`:**
    1.  **Different CWD for Proxy:** If `mcp_sse_proxy` was run from a different directory than `original_superassistant_proxy`, and `mcp-server-filesystem` was configured with a *relative* root path in `mcpconfig.json`, then the effective path served by `mcp-server-filesystem` would change.
    2.  **Configuration Error:** Double-check the `args` for `mcp-server-filesystem` in `mcpconfig.json` when using `mcp_sse_proxy`.
    3.  **Recommendation:** Configure `mcp-server-filesystem` with an **absolute root path** in `mcpconfig.json` to avoid CWD ambiguities.
        Example in `mcpconfig.json`:
        ```json
        "mcpServers": {
          "filesystem": {
            "command": "npx",
            "args": ["@modelcontextprotocol/server-filesystem", "/Users/<USER>/mcp_files_root"] // Absolute path
          }
        }
        ```

---

**5. Recommendations & Proposed Proxy Enhancement**

It's generally better to enhance the original proxy because it has broader MCP transport support. The "enhanced" one took a step back by becoming SSE-only.

**A. Enhancing the Original Proxy (`original_superassistant_proxy`):**

**Key Goals:** Add heartbeats to its server-side transports and improve logging.

```typescript
#!/usr/bin/env node

// ... (imports from original_superassistant_proxy)
// ... (add imports for Logger from mcp_sse_proxy, and getVersion if desired)

// ++ BEGIN ADDED/MODIFIED SECTION ++
// Simple logger (can be replaced with the more structured logger from mcp_sse_proxy if preferred)
const getLogger = (logLevel: 'info' | 'debug' | 'none') => {
  const prefix = '[mcp-superassistant-proxy-enhanced]';
  return {
    info: (...args: any[]) => logLevel !== 'none' && console.log(prefix, ...args),
    debug: (...args: any[]) => (logLevel === 'debug') && console.debug(prefix, '[DEBUG]', ...args),
    error: (...args: any[]) => logLevel !== 'none' && console.error(prefix, '[ERROR]', ...args),
  };
};
let logger = getLogger('info'); // Default, will be set by argv

// Heartbeat interval (milliseconds)
const HEARTBEAT_INTERVAL = 30000; // 30 seconds, make configurable via CLI/config
// ++ END ADDED/MODIFIED SECTION ++


interface MCPSuperAssistantProxyOptions {
  config: MCPConfig;
  // ++ MODIFIED ++
  logLevel: 'info' | 'debug' | 'none';
  cors: boolean;
  healthEndpoints: string[];
  timeout: number;
  heartbeatInterval?: number; // New option
}

class MCPSuperAssistantProxy {
  // ... (private members from original)
  // ++ ADDED ++
  private activeSseSessions: Map<string, { res: express.Response; heartbeatTimer?: NodeJS.Timeout }> = new Map();
  private activeStreamableSessions: Map<string, { transport: StreamableHTTPServerTransport; server: Server; createdAt: number; heartbeatTimer?: NodeJS.Timeout }> = new Map();


  constructor(options: MCPSuperAssistantProxyOptions) {
    this.config = options.config;
    this.options = options;
    // ++ MODIFIED ++
    logger = getLogger(options.logLevel); // Set logger from options
    // ... (rest of original constructor)

    // ++ MODIFIED ++ - Update activeStreamableSessions in constructor
    this.transports = {
        streamable: this.activeStreamableSessions, // Use the new Map
        sse: {} // Original SSE handling seems to use a new Server per connection, less critical for heartbeats here initially
    };
  }

  // ... (safeAsyncOperation, formatNetworkError from original)

  private setupRoutes(): void {
    // ... (health endpoints from original)

    // Unified MCP endpoint with proper session management (StreamableHTTP)
    this.app.post('/mcp', async (req, res) => {
      try {
        const sessionId = req.headers['mcp-session-id'] as string | undefined;
        let transportData = sessionId ? this.activeStreamableSessions.get(sessionId) : undefined;
        let transport: StreamableHTTPServerTransport;

        logger.debug(`POST /mcp request from ${req.ip}, sessionId: ${sessionId || 'none'}, active sessions: ${this.activeStreamableSessions.size}`);

        if (transportData) {
          transport = transportData.transport;
          logger.debug(`Reusing existing StreamableHTTP session: ${sessionId}`);
          // ++ ADDED ++ Reset heartbeat on activity
          if (transportData.heartbeatTimer) clearTimeout(transportData.heartbeatTimer);
          transportData.heartbeatTimer = this.startStreamableHeartbeat(sessionId!, transport, transportData.server);
        } else if (!sessionId && isInitializeRequest(req.body)) {
          // ... (original logic for new session server and transport creation)
          // ++ MODIFIED ++
          const sessionServer = new Server(/* ... */);
          this.copyServerHandlers(sessionServer); // Ensure handlers are copied

          transport = new StreamableHTTPServerTransport({
            sessionIdGenerator: () => randomUUID(),
            onsessioninitialized: (newSessionId) => {
              this.activeStreamableSessions.set(newSessionId, { // Store in the new map
                transport,
                server: sessionServer,
                createdAt: Date.now(),
                heartbeatTimer: this.startStreamableHeartbeat(newSessionId, transport, sessionServer) // Start heartbeat
              });
              logger.debug(`New Streamable HTTP session initialized: ${newSessionId}`);
            }
          });

          transport.onclose = () => {
            if (transport.sessionId) {
              logger.debug(`Streamable HTTP session closed: ${transport.sessionId}`);
              const sessionToClear = this.activeStreamableSessions.get(transport.sessionId);
              if (sessionToClear?.heartbeatTimer) clearTimeout(sessionToClear.heartbeatTimer);
              this.activeStreamableSessions.delete(transport.sessionId);
            }
          };
          await sessionServer.connect(transport);
        } else {
          // ... (original invalid request logic)
          return;
        }
        await transport.handleRequest(req, res, req.body);
      } catch (error) {
        logger.error(`Error handling MCP request from ${req.ip}:`, error);
        // ... (original error response)
      }
    });

    // ... (original GET /mcp and DELETE /mcp routes, ensure they also manage heartbeats if applicable)
    // Example for GET /mcp (SSE part of StreamableHTTP)
    this.app.get('/mcp', async (req, res) => {
      try {
        const sessionId = req.headers['mcp-session-id'] as string | undefined;
        if (!sessionId || !this.activeStreamableSessions.has(sessionId)) { // Check new map
          res.status(400).send('Invalid or missing session ID');
          return;
        }
        const transportData = this.activeStreamableSessions.get(sessionId)!;
        // ++ ADDED ++ Reset heartbeat on activity
        if (transportData.heartbeatTimer) clearTimeout(transportData.heartbeatTimer);
        transportData.heartbeatTimer = this.startStreamableHeartbeat(sessionId, transportData.transport, transportData.server);

        await transportData.transport.handleRequest(req, res);
      } catch (error) {
        logger.error('Error handling GET /mcp request:', error);
        res.status(500).send('Internal server error');
      }
    });


    // Backward compatibility: SSE endpoint for legacy clients
    this.app.get('/sse', async (req, res) => {
      let sessionId: string | undefined;
      try {
        logger.debug('New SSE connection from', req.ip);
        // ... (original SSE headers)

        const sseTransport = new SSEServerTransport(/* ... */);
        const sseServer = new Server(/* ... */);
        this.copyServerHandlers(sseServer);
        await sseServer.connect(sseTransport);
        sessionId = sseTransport.sessionId;

        if (sessionId) {
          // ++ MODIFIED ++
          const heartbeatTimer = this.startSseHeartbeat(sessionId, res);
          this.activeSseSessions.set(sessionId, { res, heartbeatTimer });
          logger.debug(`SSE session created: ${sessionId}`);
        }

        // ... (original onclose, onerror, req.on('close'))
        // ++ MODIFIED ++ Ensure heartbeat cleanup
        const cleanupSse = () => {
            if (sessionId) {
                const sessionData = this.activeSseSessions.get(sessionId);
                if (sessionData?.heartbeatTimer) clearTimeout(sessionData.heartbeatTimer);
                this.activeSseSessions.delete(sessionId);
                logger.debug(`Cleaned up SSE session: ${sessionId}`);
            }
        };
        sseTransport.onclose = () => { logger.debug(`SSE transport closed (session ${sessionId})`); cleanupSse(); };
        sseTransport.onerror = (err) => { logger.error(`SSE transport error (session ${sessionId}): ${this.formatNetworkError(err)}`); cleanupSse(); };
        req.on('close', () => { logger.debug(`SSE client disconnected (session ${sessionId})`); cleanupSse(); });
        req.on('error', (err: any) => { /* ... */ cleanupSse(); });

      } catch (error) {
        // ... (original error handling)
        if (sessionId) { // Ensure cleanup if session was partially created
            const sessionData = this.activeSseSessions.get(sessionId);
            if (sessionData?.heartbeatTimer) clearTimeout(sessionData.heartbeatTimer);
            this.activeSseSessions.delete(sessionId);
        }
      }
    });

    // Handle POST requests to SSE endpoint (for clients trying StreamableHTTP on SSE endpoint)
    this.app.post('/sse', /* ... original ... */);

    // Backward compatibility: Messages endpoint for SSE transport
    this.app.post('/messages', async (req, res) => {
        try {
            const sessionId = req.query.sessionId as string;
            // ...
            const session = this.transports.sse[sessionId]; // Original map for this logic
                                                            // Or adapt to use activeSseSessions if that's preferred for /messages too
            if (session?.transport?.handlePostMessage) {
                // ++ ADDED ++ Reset SSE heartbeat on activity
                const activeSseSession = this.activeSseSessions.get(sessionId);
                if (activeSseSession?.heartbeatTimer) {
                    clearTimeout(activeSseSession.heartbeatTimer);
                    activeSseSession.heartbeatTimer = this.startSseHeartbeat(sessionId, activeSseSession.res);
                }
                // ... (original handlePostMessage)
            }
            // ...
        } catch (error) {
            // ...
        }
    });
  }

  // ++ ADDED ++ Method to start SSE heartbeat
  private startSseHeartbeat(sessionId: string, res: express.Response): NodeJS.Timeout {
    const sendHeartbeat = () => {
      if (this.activeSseSessions.has(sessionId) && !res.writableEnded) {
        try {
          res.write(':heartbeat\n\n'); // SSE comment as heartbeat
          logger.debug(`SSE Heartbeat sent to session ${sessionId}`);
          // Schedule next heartbeat
          const currentSession = this.activeSseSessions.get(sessionId);
          if (currentSession) { // Check if session still exists
            currentSession.heartbeatTimer = this.startSseHeartbeat(sessionId, res);
          }
        } catch (err) {
          logger.error(`Error sending SSE heartbeat to session ${sessionId}:`, err);
          const sessionData = this.activeSseSessions.get(sessionId);
          if (sessionData?.heartbeatTimer) clearTimeout(sessionData.heartbeatTimer);
          this.activeSseSessions.delete(sessionId);
        }
      } else {
        logger.debug(`SSE session ${sessionId} no longer active or writable, stopping heartbeat.`);
        const sessionData = this.activeSseSessions.get(sessionId);
        if (sessionData?.heartbeatTimer) clearTimeout(sessionData.heartbeatTimer);
        this.activeSseSessions.delete(sessionId);
      }
    };
    return setTimeout(sendHeartbeat, this.options.heartbeatInterval || HEARTBEAT_INTERVAL);
  }

  // ++ ADDED ++ Method to start StreamableHTTP heartbeat (more complex)
  // For StreamableHTTP, heartbeats ideally should be MCP messages (e.g., notifications)
  // This is a simplified version; a robust one would involve sending actual MCP keep-alive messages.
  private startStreamableHeartbeat(sessionId: string, transport: StreamableHTTPServerTransport, server: Server): NodeJS.Timeout {
    const sendKeepAlive = async () => {
      if (this.activeStreamableSessions.has(sessionId) && transport.isConnected) {
        try {
          // Example: Send a custom notification (client should be designed to ignore or handle this)
          // Or, if the SDK supports a ping/pong or a no-op request, use that.
          // For simplicity, this example doesn't send a real MCP message.
          // A more robust solution would be needed for true StreamableHTTP keep-alive.
          // This primarily resets the timeout for server-side cleanup of the session.
          logger.debug(`StreamableHTTP keep-alive for session ${sessionId}`);

          const currentSession = this.activeStreamableSessions.get(sessionId);
          if (currentSession) { // Check if session still exists
            currentSession.heartbeatTimer = this.startStreamableHeartbeat(sessionId, transport, server);
          }
        } catch (err) {
          logger.error(`Error in StreamableHTTP keep-alive for session ${sessionId}:`, err);
          const sessionData = this.activeStreamableSessions.get(sessionId);
          if (sessionData?.heartbeatTimer) clearTimeout(sessionData.heartbeatTimer);
          this.activeStreamableSessions.delete(sessionId);
        }
      } else {
        logger.debug(`StreamableHTTP session ${sessionId} no longer active, stopping keep-alive.`);
        const sessionData = this.activeStreamableSessions.get(sessionId);
        if (sessionData?.heartbeatTimer) clearTimeout(sessionData.heartbeatTimer);
        this.activeStreamableSessions.delete(sessionId);
      }
    };
    return setTimeout(sendKeepAlive, this.options.heartbeatInterval || HEARTBEAT_INTERVAL);
  }


  // ... (setupServerHandlers, copyServerHandlers from original)

  // ... (connectWithBackwardsCompatibility, initialize, connectToServer from original)
  // Modify connectToServer and connectWithBackwardsCompatibility to use the logger.

  private async connectToServer(serverName: string, config: MCPServerConfig): Promise<void> {
    // ... original logic ...
    logger.info(`Connecting to ${serverName} (${transportType}${!config.type ? ' - inferred' : ''})...`);
    client.onerror = (error) => {
      const errorMsg = this.formatNetworkError(error);
      logger.error(`Client connection error for ${serverName}: ${errorMsg}`); // Use logger
    };
    // ... rest of the original logic ...
    logger.info(`Connected to ${serverName}: ${connectedServer.tools.length} tools, ${connectedServer.resources.length} resources, ${connectedServer.prompts.length} prompts`);
  }


  // ... (terminateChildProcess, start, stop from original)
  // Modify stop to clear new heartbeat timers
  async stop(): Promise<void> {
    logger.info("Starting graceful shutdown..."); // Use logger

    // ... (original cleanupPromises for connectedServers)

    // Clean up all SSE sessions
    this.activeSseSessions.forEach((session, sessionId) => {
        if (session.heartbeatTimer) clearTimeout(session.heartbeatTimer);
        // session.res.end(); // Might be too abrupt, let transport.close handle it
        logger.debug(`Cleared SSE heartbeat for session ${sessionId}`);
    });
    this.activeSseSessions.clear();

    // Clean up Streamable HTTP sessions
    this.activeStreamableSessions.forEach((session, sessionId) => {
        if (session.heartbeatTimer) clearTimeout(session.heartbeatTimer);
        // session.transport.close(); // Let the main server close handle this
        logger.debug(`Cleared StreamableHTTP heartbeat for session ${sessionId}`);
    });
    this.activeStreamableSessions.clear();


    // ... (rest of original stop method, ensuring logger is used)
    logger.info("MCP SuperAssistant Proxy stopped");
  }
}


async function main() {
  const argv = yargs(hideBin(process.argv))
    // ... (original yargs options)
    // ++ ADDED ++
    .option('heartbeatInterval', {
      type: 'number',
      description: 'Heartbeat interval in milliseconds for SSE connections',
      default: HEARTBEAT_INTERVAL
    })
    .parseSync();

  // ++ MODIFIED ++ Use the global logger
  logger = getLogger(argv.debug ? 'debug' : (argv.logLevel as 'info' | 'debug' | 'none'));

  try {
    // ... (original config loading)

    const options: MCPSuperAssistantProxyOptions = {
      config,
      logLevel: argv.debug ? 'debug' : (argv.logLevel as 'info' | 'debug' | 'none'),
      cors: argv.cors,
      healthEndpoints: (argv.healthEndpoint as string[]) || [],
      timeout: argv.timeout,
      heartbeatInterval: argv.heartbeatInterval // Pass heartbeat interval
    };

    const mcpsuperassistantproxy = new MCPSuperAssistantProxy(options);
    // ... (rest of original main function, ensure logger is used)
  } catch (error) {
    logger.error("Failed to start MCP SuperAssistant proxy:", error); // Use logger
    process.exit(1);
  }
}

// ... (original help text and main execution)
// Add --heartbeatInterval to help text

main().catch(error => logger.error(error)); // Use logger

```

**Explanation of Proxy Enhancements:**

1.  **Logger:** Integrated a logger similar to `mcp_sse_proxy`. All `console.log/error/debug` calls are replaced with `logger.info/error/debug`.
2.  **Heartbeat for SSE (`/sse` endpoint):**
    *   When a new SSE connection is established, `startSseHeartbeat` is called.
    *   It sends an SSE comment (`:heartbeat\n\n`) to the client every `HEARTBEAT_INTERVAL`. SSE clients typically ignore comments but treat them as keep-alive signals.
    *   If sending the heartbeat fails (e.g., connection closed), the session and its timer are cleaned up.
    *   Activity on the `/messages` POST endpoint for an SSE session also resets its heartbeat timer.
3.  **Heartbeat/Keep-Alive for StreamableHTTP (`/mcp` endpoint):**
    *   This is more conceptual for StreamableHTTP as true heartbeats should be MCP-level messages.
    *   The `startStreamableHeartbeat` function is added. In this simplified version, it mainly serves to reset a server-side timeout for cleaning up inactive `StreamableHTTPServerTransport` instances if they were to be implemented.
    *   **Important:** For robust StreamableHTTP keep-alive, the proxy would need to send actual MCP notifications (e.g., a custom `mcp/keepalive` notification) that the SDK client would handle (or ignore if unknown). The SDK's `StreamableHTTPClientTransport` might have its own HTTP/2 keep-alive mechanisms, but application-level heartbeats provide an extra layer of assurance.
    *   Activity on POST or GET to `/mcp` for a session resets its keep-alive timer.
4.  **Cleanup:** The `stop()` method is updated to clear these new heartbeat timers.
5.  **Configuration:** Added `--heartbeatInterval` CLI option.

**B. Chrome Extension (`officialmcpclient.ts` - `PersistentMcpClient`):**

The primary issue here is the lack of client-initiated keep-alives and the predominantly user-driven reconnection.

**Suggestions for `PersistentMcpClient`:**

1.  **Client-Side Heartbeats (More Involved Change):**
    *   Add a timer within `PersistentMcpClient`.
    *   Periodically (e.g., every 25 seconds, less than the proxy's `HEARTBEAT_INTERVAL`), if connected, send a lightweight MCP message. This could be:
        *   A custom MCP notification (e.g., `{jsonrpc: "2.0", method: "mcp/client_heartbeat", params: {timestamp: Date.now()}}`). The proxy doesn't need to specifically handle this; just receiving traffic keeps the connection alive.
        *   A standard, low-impact request like `mcp/capabilities` if you don't mind the overhead, though a notification is better.
    *   If sending this heartbeat fails with a connection error, then proactively mark the connection as disconnected (`this.isConnected = false; ...`) and potentially trigger a limited auto-reconnect sequence.

2.  **More Robust (but still Limited) Automatic Reconnection:**
    *   Instead of `scheduleReconnect` doing nothing, allow it to try reconnecting a few times with increasing backoff *after* a connection has been established and then dropped.
    *   Modify `createConnection`: if it fails and `this.reconnectAttempts < this.maxReconnectAttempts`, it could call `scheduleReconnect`.
    *   `scheduleReconnect` would then use `setTimeout` with exponential backoff to call `this.connect(this.serverUrl)` again.
    *   This should be careful not to get into infinite loops if the server is genuinely down. The existing `consecutiveFailures` and `circuitBreakerOpen` logic in the extension for initial connections is good and could be adapted.

    ```typescript
    // In PersistentMcpClient

    // Modify scheduleReconnect
    private scheduleReconnect(): void {
        if (this.reconnectTimeoutId) {
            clearTimeout(this.reconnectTimeoutId);
        }

        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.log('[PersistentMcpClient] Max auto-reconnect attempts reached after connection drop. Waiting for user action.');
            this.isConnected = false; // Ensure status reflects this
            // Consider notifying McpInterface to update UI properly
            // mcpInterface.updateConnectionStatus(false); // If accessible or via callback
            return;
        }

        this.reconnectAttempts++;
        const delay = Math.min(this.reconnectDelay * Math.pow(1.5, this.reconnectAttempts), 30000); // Cap at 30s
        console.log(`[PersistentMcpClient] Scheduling auto-reconnect attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts} in ${delay / 1000}s`);

        this.reconnectTimeoutId = setTimeout(async () => {
            this.reconnectTimeoutId = null;
            if (this.serverUrl && !this.isConnected) { // Only if still disconnected
                console.log(`[PersistentMcpClient] Attempting auto-reconnect to ${this.serverUrl}`);
                try {
                    await this.connect(this.serverUrl); // connect will reset attempts on success
                } catch (error) {
                    // Connect already logs and might schedule another if attempts remain
                    console.error('[PersistentMcpClient] Auto-reconnect attempt failed.');
                     // If connect itself doesn't schedule, and attempts remain, schedule again
                    if (this.reconnectAttempts < this.maxReconnectAttempts && !this.isConnected) {
                        this.scheduleReconnect();
                    } else if (!this.isConnected) {
                         mcpInterface.updateConnectionStatus(false); // Ensure UI knows we gave up
                    }
                }
            }
        }, delay);
    }

    // In createConnection, upon error before throwing:
    // ... inside catch (error)
    // if (this.reconnectAttempts < this.maxReconnectAttempts && !this.isConnected) {
    //    this.scheduleReconnect(); // If connect fails, and it's a scenario where auto-reconnect is desired
    // }
    // throw enhancedError;

    // And when a connection error is detected in callTool/getPrimitives:
    // if (this.isConnectionError(errorMessage)) {
    //     console.warn('[PersistentMcpClient] Connection error detected, marking as disconnected.');
    //     this.isConnected = false;
    //     this.client = null;
    //     this.transport = null;
    //     this.connectionPromise = null;
    //     this.reconnectAttempts = 0; // Reset attempts for a new sequence of auto-reconnects
    //     this.scheduleReconnect(); // Start auto-reconnect sequence
    // }
    ```
    This needs careful implementation to avoid race conditions and ensure `reconnectAttempts` is managed correctly across different failure points. The goal is a limited auto-retry *after a previously good connection drops*.

**C. Path Issue with `mcp-server-filesystem`:**

*   **Verify Proxy CWD:** When running the proxy (either version), `console.log(process.cwd())` at the start to see its current working directory.
*   **Absolute Path for Filesystem Server:**
    In your `mcpconfig.json`, for the `mcp-server-filesystem` entry, ensure the path argument is absolute.
    ```json
    {
      "mcpServers": {
        "filesystem": {
          "command": "npx",
          "args": ["@modelcontextprotocol/server-filesystem", "/Users/<USER>/Documents/MyMCPFilesRoot"] // Use an ABSOLUTE path here
          // or if it takes options:
          // "args": ["@modelcontextprotocol/server-filesystem", "--root", "/Users/<USER>/Documents/MyMCPFilesRoot"]
        }
        // ... other servers
      }
    }
    ```
    This makes the filesystem server's behavior independent of where the proxy script is launched.
*   **Tool Call Arguments:** When your extension (or the AI) generates tool calls for `filesystem.read_file` or similar, the `path` argument provided should be relative *to the root you configured for `mcp-server-filesystem`*.
    For example, if root is `/Users/<USER>/Documents/MyMCPFilesRoot` and you want to read `/Users/<USER>/Documents/MyMCPFilesRoot/projects/notes.txt`, the tool call argument should be `{"path": "projects/notes.txt"}`.

---

**Summary of Recommendations:**

1.  **Use and Enhance the Original Proxy:** It has better MCP transport support.
    *   Add robust logging (like in `mcp_sse_proxy`).
    *   Implement server-side heartbeats for its SSE transport (sending `:\n\n` comments).
    *   Consider how to implement keep-alives for its StreamableHTTP transport (potentially MCP-level notifications if the SDK client can ignore them, or rely on HTTP/2 keep-alives if sufficient).
    *   Use the original's `terminateChildProcess` for better stdio server cleanup.
    *   Ensure it uses `StdioClientTransport` for stdio backends.
2.  **Enhance Chrome Extension's `PersistentMcpClient`:**
    *   **Option A (Preferred for Max Stability):** Implement client-to-proxy heartbeats. Send a lightweight MCP message periodically to the proxy to keep the TCP connection active.
    *   **Option B (Simpler):** Implement a more robust (but still limited to a few tries) automatic reconnection mechanism *after* a previously established connection drops.
3.  **Fix Path Issues:**
    *   Configure `mcp-server-filesystem` in your `mcpconfig.json` with an **absolute root path**. This is the most reliable way to ensure consistent behavior regardless of the proxy's CWD.
    *   Ensure the paths provided in tool call arguments are relative to this configured absolute root.

By making the proxy's server-side transports more resilient with heartbeats, and optionally making the extension's client a bit more proactive in keeping the connection alive or retrying, you should significantly reduce the "Server Disconnected" occurrences. For path issues, absolute configuration is key.

The analysis suggests the "Server Disconnected" issue is more likely a combination of the original proxy not sending keep-alives and the extension not sending them either/not auto-reconnecting robustly, leading to silent TCP drops. The path issue is likely a configuration/CWD matter for the filesystem server, not something the proxy itself is breaking.